import React from 'react';
import { View, Text, StyleSheet, Switch, ScrollView, Image, TouchableOpacity } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { Card } from '@/components/Card';
import { Button } from '@/components/Button';
import { useTheme } from '@/context/ThemeContext';
import { useAuth } from '@/context/AuthContext';
import { Moon, Sun, User, Shield, Mail, ChevronRight, LogOut } from 'lucide-react-native';

export default function SettingsScreen() {
  const { colors, isDarkMode, toggleTheme } = useTheme();
  const { user, logout } = useAuth();
  
  return (
    <ScrollView 
      style={[styles.container, { backgroundColor: colors.background }]}
      contentContainerStyle={styles.contentContainer}
    >
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />
      
      <View style={styles.headerContainer}>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Settings</Text>
      </View>
      
      <Card style={styles.profileCard}>
        <View style={styles.profileHeader}>
          <Image
            source={{ uri: user?.avatar || 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg' }}
            style={styles.profileImage}
          />
          <View style={styles.profileInfo}>
            <Text style={[styles.profileName, { color: colors.text }]}>
              {user?.firstName} {user?.lastName}
            </Text>
            <Text style={[styles.profileRole, { color: colors.textSecondary }]}>
              {user?.role.charAt(0).toUpperCase() + user?.role.slice(1)}
            </Text>
          </View>
          <TouchableOpacity 
            style={[styles.editButton, { backgroundColor: colors.primaryLight }]}
            onPress={() => {}}
          >
            <Text style={[styles.editButtonText, { color: colors.primary }]}>Edit</Text>
          </TouchableOpacity>
        </View>
        
        <View style={[styles.divider, { backgroundColor: colors.border }]} />
        
        <View style={styles.profileDetails}>
          <View style={styles.profileDetailItem}>
            <Mail size={20} color={colors.textSecondary} style={styles.profileDetailIcon} />
            <Text style={[styles.profileDetailText, { color: colors.text }]}>{user?.email}</Text>
          </View>
        </View>
      </Card>
      
      <Text style={[styles.sectionTitle, { color: colors.text }]}>Appearance</Text>
      
      <Card style={styles.settingCard}>
        <View style={styles.settingItem}>
          <View style={styles.settingLeft}>
            {isDarkMode ? (
              <Moon size={20} color={colors.text} style={styles.settingIcon} />
            ) : (
              <Sun size={20} color={colors.text} style={styles.settingIcon} />
            )}
            <Text style={[styles.settingText, { color: colors.text }]}>Dark Mode</Text>
          </View>
          <Switch
            value={isDarkMode}
            onValueChange={toggleTheme}
            trackColor={{ false: '#D1D5DB', true: colors.primary }}
            thumbColor="#FFFFFF"
          />
        </View>
      </Card>
      
      <Text style={[styles.sectionTitle, { color: colors.text }]}>Account</Text>
      
      <Card style={styles.settingCard}>
        <TouchableOpacity style={styles.settingItem} onPress={() => {}}>
          <View style={styles.settingLeft}>
            <User size={20} color={colors.text} style={styles.settingIcon} />
            <Text style={[styles.settingText, { color: colors.text }]}>Personal Information</Text>
          </View>
          <ChevronRight size={20} color={colors.textSecondary} />
        </TouchableOpacity>
        
        <View style={[styles.itemDivider, { backgroundColor: colors.border }]} />
        
        <TouchableOpacity style={styles.settingItem} onPress={() => {}}>
          <View style={styles.settingLeft}>
            <Shield size={20} color={colors.text} style={styles.settingIcon} />
            <Text style={[styles.settingText, { color: colors.text }]}>Security</Text>
          </View>
          <ChevronRight size={20} color={colors.textSecondary} />
        </TouchableOpacity>
      </Card>
      
      <View style={styles.logoutContainer}>
        <Button
          title="Log Out"
          onPress={logout}
          variant="outline"
          fullWidth
          leftIcon={<LogOut size={20} color={colors.error} />}
          style={{ borderColor: colors.error }}
          textStyle={{ color: colors.error }}
        />
      </View>
      
      <Text style={[styles.versionText, { color: colors.textSecondary }]}>
        BuildPro v1.0.0
      </Text>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  headerContainer: {
    marginBottom: 24,
  },
  headerTitle: {
    fontFamily: 'Inter-Bold',
    fontSize: 28,
  },
  profileCard: {
    marginBottom: 24,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
  },
  profileInfo: {
    marginLeft: 16,
    flex: 1,
  },
  profileName: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 18,
    marginBottom: 4,
  },
  profileRole: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
  },
  editButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
  },
  editButtonText: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
  },
  divider: {
    height: 1,
    marginVertical: 16,
  },
  profileDetails: {
    gap: 12,
  },
  profileDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileDetailIcon: {
    marginRight: 12,
  },
  profileDetailText: {
    fontFamily: 'Inter-Regular',
    fontSize: 16,
  },
  sectionTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 18,
    marginBottom: 12,
    marginTop: 12,
  },
  settingCard: {
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingIcon: {
    marginRight: 12,
  },
  settingText: {
    fontFamily: 'Inter-Medium',
    fontSize: 16,
  },
  itemDivider: {
    height: 1,
    marginVertical: 8,
  },
  logoutContainer: {
    marginTop: 24,
    marginBottom: 24,
  },
  versionText: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    textAlign: 'center',
  },
});